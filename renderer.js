const { ipc<PERSON><PERSON><PERSON> } = require('electron');

let monitoring = false;
let config = {};

// Initialize app
document.addEventListener('DOMContentLoaded', async () => {
    config = await ipc<PERSON>enderer.invoke('get-config');
    loadConfigToUI();
    log('Application started');
});

// Listen for temperature updates
ipcRenderer.on('temperature-update', (event, temps) => {
    updateTemperatureDisplay(temps);
});

async function toggleMonitoring() {
    const startBtn = document.getElementById('start-btn');
    
    if (!monitoring) {
        await ipcRenderer.invoke('start-monitoring');
        monitoring = true;
        startBtn.textContent = 'Stop Monitoring';
        startBtn.classList.remove('btn-primary');
        startBtn.classList.add('btn-secondary');
        log('Monitoring started');
        
        // Start getting temperature updates
        getTemperatureUpdate();
    } else {
        await ipcRenderer.invoke('stop-monitoring');
        monitoring = false;
        startBtn.textContent = 'Start Monitoring';
        startBtn.classList.remove('btn-secondary');
        startBtn.classList.add('btn-primary');
        log('Monitoring stopped');
        
        // Reset status indicators
        resetStatusIndicators();
    }
}

async function getTemperatureUpdate() {
    if (!monitoring) return;
    
    try {
        const temps = await ipcRenderer.invoke('get-temperatures');
        updateTemperatureDisplay(temps);
    } catch (error) {
        log(`Error getting temperatures: ${error.message}`, 'error');
    }
    
    // Schedule next update
    if (monitoring) {
        setTimeout(getTemperatureUpdate, config.refresh_interval * 1000);
    }
}

function updateTemperatureDisplay(temps) {
    const cpuTempEl = document.getElementById('cpu-temp');
    const hddTempEl = document.getElementById('hdd-temp');
    const cpuUsageEl = document.getElementById('cpu-usage');
    const ramUsageEl = document.getElementById('ram-usage');
    const hddUsageEl = document.getElementById('hdd-usage');
    const mainStatusEl = document.getElementById('main-status');
    const statusMessageEl = document.getElementById('status-message');

    let systemAlert = false;
    
    // Update CPU temperature
    if (temps.cpu !== null && temps.cpu !== undefined) {
        cpuTempEl.textContent = `${temps.cpu.toFixed(1)}°C`;
        cpuTempEl.className = 'stat-value';

        if (temps.cpu >= config.cpu_threshold) {
            cpuTempEl.classList.add('stat-danger');
            systemAlert = true;
        } else if (temps.cpu >= config.cpu_threshold - 10) {
            cpuTempEl.classList.add('stat-warning');
        } else {
            cpuTempEl.classList.add('stat-normal');
        }
    } else {
        cpuTempEl.textContent = '--°C';
        cpuTempEl.className = 'stat-value stat-offline';
    }
    
    // Update HDD temperature
    if (temps.hdd !== null && temps.hdd !== undefined) {
        hddTempEl.textContent = `${temps.hdd.toFixed(1)}°C`;
        hddTempEl.className = 'stat-value';

        if (temps.hdd >= config.hdd_threshold) {
            hddTempEl.classList.add('stat-danger');
            systemAlert = true;
        } else if (temps.hdd >= config.hdd_threshold - 5) {
            hddTempEl.classList.add('stat-warning');
        } else {
            hddTempEl.classList.add('stat-normal');
        }
    } else {
        hddTempEl.textContent = '--°C';
        hddTempEl.className = 'stat-value stat-offline';
    }

    // Update CPU Usage
    if (temps.cpuUsage !== null && temps.cpuUsage !== undefined) {
        cpuUsageEl.textContent = `${temps.cpuUsage}%`;
        cpuUsageEl.className = 'stat-value';

        if (temps.cpuUsage >= config.cpu_usage_threshold) {
            cpuUsageEl.classList.add('stat-danger');
            systemAlert = true;
        } else if (temps.cpuUsage >= config.cpu_usage_threshold - 15) {
            cpuUsageEl.classList.add('stat-warning');
        } else {
            cpuUsageEl.classList.add('stat-normal');
        }
    } else {
        cpuUsageEl.textContent = '--%';
        cpuUsageEl.className = 'stat-value stat-offline';
    }

    // Update RAM Usage
    if (temps.ramUsage !== null && temps.ramUsage !== undefined) {
        ramUsageEl.textContent = `${temps.ramUsage}%`;
        ramUsageEl.className = 'stat-value';

        if (temps.ramUsage >= config.ram_usage_threshold) {
            ramUsageEl.classList.add('stat-danger');
            systemAlert = true;
        } else if (temps.ramUsage >= config.ram_usage_threshold - 10) {
            ramUsageEl.classList.add('stat-warning');
        } else {
            ramUsageEl.classList.add('stat-normal');
        }
    } else {
        ramUsageEl.textContent = '--%';
        ramUsageEl.className = 'stat-value stat-offline';
    }

    // Update HDD Usage
    if (temps.hddUsage !== null && temps.hddUsage !== undefined) {
        hddUsageEl.textContent = `${temps.hddUsage}%`;
        hddUsageEl.className = 'stat-value';

        if (temps.hddUsage >= config.disk_usage_threshold) {
            hddUsageEl.classList.add('stat-danger');
            systemAlert = true;
        } else if (temps.hddUsage >= config.disk_usage_threshold - 10) {
            hddUsageEl.classList.add('stat-warning');
        } else {
            hddUsageEl.classList.add('stat-normal');
        }
    } else {
        hddUsageEl.textContent = '--%';
        hddUsageEl.className = 'stat-value stat-offline';
    }
    
    // Update system status
    if (systemAlert) {
        mainStatusEl.className = 'status-indicator status-alert';
        statusMessageEl.textContent = 'System Alert!';
    } else if (monitoring) {
        mainStatusEl.className = 'status-indicator status-normal';
        statusMessageEl.textContent = 'System Normal';
    } else {
        mainStatusEl.className = 'status-indicator status-offline';
        statusMessageEl.textContent = 'System Offline';
    }
    
    // Console log for debugging
    if (systemAlert) {
        console.log('System Alert detected!');
    }
}

function resetStatusIndicators() {
    document.getElementById('main-status').className = 'status-indicator status-offline';
    document.getElementById('status-message').textContent = 'System Offline';

    document.getElementById('cpu-temp').textContent = '--°C';
    document.getElementById('cpu-temp').className = 'stat-value stat-offline';
    document.getElementById('hdd-temp').textContent = '--°C';
    document.getElementById('hdd-temp').className = 'stat-value stat-offline';

    document.getElementById('cpu-usage').textContent = '--%';
    document.getElementById('cpu-usage').className = 'stat-value stat-offline';
    document.getElementById('ram-usage').textContent = '--%';
    document.getElementById('ram-usage').className = 'stat-value stat-offline';
    document.getElementById('hdd-usage').textContent = '--%';
    document.getElementById('hdd-usage').className = 'stat-value stat-offline';
}

function openSettings() {
    loadConfigToUI();
    document.getElementById('settings-modal').style.display = 'block';
}

function closeSettings() {
    document.getElementById('settings-modal').style.display = 'none';
}

function loadConfigToUI() {
    document.getElementById('pc-name').value = config.pc_name || 'My PC';
    document.getElementById('telegram-token').value = config.telegram_token || '';
    document.getElementById('telegram-chat-id').value = config.telegram_chat_id || '';
    document.getElementById('refresh-interval').value = config.refresh_interval || 30;
    document.getElementById('alert-cooldown').value = config.alert_cooldown || 60;
    document.getElementById('cpu-threshold').value = config.cpu_threshold || 80;
    document.getElementById('hdd-threshold').value = config.hdd_threshold || 50;
    document.getElementById('cpu-usage-threshold').value = config.cpu_usage_threshold || 85;
    document.getElementById('ram-usage-threshold').value = config.ram_usage_threshold || 90;
    document.getElementById('disk-usage-threshold').value = config.disk_usage_threshold || 95;

    document.getElementById('cpu-temp-alert').checked = config.cpu_temp_alert !== false;
    document.getElementById('hdd-temp-alert').checked = config.hdd_temp_alert !== false;
    document.getElementById('cpu-usage-alert').checked = config.cpu_usage_alert !== false;
    document.getElementById('ram-usage-alert').checked = config.ram_usage_alert !== false;
    document.getElementById('disk-usage-alert').checked = config.disk_usage_alert !== false;

    // Update PC name display
    updatePCNameDisplay();
}

function updatePCNameDisplay() {
    const pcNameDisplay = document.getElementById('pc-name-display');
    if (pcNameDisplay) {
        pcNameDisplay.textContent = config.pc_name || 'My PC';
    }
}

async function saveSettings() {
    const newConfig = {
        pc_name: document.getElementById('pc-name').value,
        telegram_token: document.getElementById('telegram-token').value,
        telegram_chat_id: document.getElementById('telegram-chat-id').value,
        refresh_interval: parseInt(document.getElementById('refresh-interval').value),
        alert_cooldown: parseInt(document.getElementById('alert-cooldown').value),
        cpu_threshold: parseInt(document.getElementById('cpu-threshold').value),
        hdd_threshold: parseInt(document.getElementById('hdd-threshold').value),
        cpu_usage_threshold: parseInt(document.getElementById('cpu-usage-threshold').value),
        ram_usage_threshold: parseInt(document.getElementById('ram-usage-threshold').value),
        disk_usage_threshold: parseInt(document.getElementById('disk-usage-threshold').value),
        cpu_temp_alert: document.getElementById('cpu-temp-alert').checked,
        hdd_temp_alert: document.getElementById('hdd-temp-alert').checked,
        cpu_usage_alert: document.getElementById('cpu-usage-alert').checked,
        ram_usage_alert: document.getElementById('ram-usage-alert').checked,
        disk_usage_alert: document.getElementById('disk-usage-alert').checked
    };

    try {
        await ipcRenderer.invoke('save-config', newConfig);
        config = { ...config, ...newConfig };
        updatePCNameDisplay();
        closeSettings();
        console.log('Settings saved successfully');
    } catch (error) {
        console.error(`Error saving settings: ${error.message}`);
    }
}

async function testTelegram() {
    const token = document.getElementById('telegram-token').value;
    const chatId = document.getElementById('telegram-chat-id').value;
    const pcName = document.getElementById('pc-name').value || 'My PC';

    if (!token || !chatId) {
        alert('Please enter both Bot Token and Chat ID');
        return;
    }

    try {
        const result = await ipcRenderer.invoke('test-telegram', token, chatId);

        if (result.success) {
            alert('Test message sent successfully!');
            console.log('Telegram test successful');
        } else {
            alert(`Failed to send test message: ${result.error}`);
            console.error(`Telegram test failed: ${result.error}`);
        }
    } catch (error) {
        alert(`Error testing Telegram: ${error.message}`);
        console.error(`Telegram test error: ${error.message}`);
    }
}



// Close modal when clicking outside
document.getElementById('settings-modal').addEventListener('click', (e) => {
    if (e.target === document.getElementById('settings-modal')) {
        closeSettings();
    }
});
