# Experimental

Experimental features are things we are trying out. We are **not** sure
if they'll become stable and move into the `master` branch.
Try them out and give feedback to support stabilizing them.

* [Features](#features)
* [API Reference][api-experimental]
* [Installation](#installation)


<a name="features"></a>
## features:

* Support API method `deleteMessage`

Open issues tagged `experimental`: [link](https://github.com/yagop/node-telegram-bot-api/issues?q=is%3Apr+is%3Aopen+label%3Aexperimental)


<a name="installation"></a>
## installation:

```bash
$ npm install yagop/node-telegram-bot-api#experimental
```


[api-experimental]:https://github.com/yagop/node-telegram-bot-api/tree/experimental/doc/api.md
