# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/)
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [v1.0.2](https://github.com/inspect-js/data-view-buffer/compare/v1.0.1...v1.0.2) - 2024-12-19

### Commits

- [actions] split out node 10-20, and 20+ [`f99dc3f`](https://github.com/inspect-js/data-view-buffer/commit/f99dc3f4c0edc7ffa3cc96302b1c0c96663d0a48)
- [types] use shared tsconfig [`9ad8630`](https://github.com/inspect-js/data-view-buffer/commit/9ad86303de5ccef0daf248177b50f33316d1b3d5)
- [readme] update URLs [`d3c66fb`](https://github.com/inspect-js/data-view-buffer/commit/d3c66fb4426b8d57834dea4842a06ef4ad7c4695)
- [Dev Deps] update `@ljharb/eslint-config`, `@ljharb/tsconfig`, `@types/object-inspect`, `@types/tape`, `auto-changelog`, `es-value-fixtures`, `object-inspect`, `tape` [`7dda732`](https://github.com/inspect-js/data-view-buffer/commit/7dda732235b65fe23608ac8a0a55207c53236a20)
- [Refactor] use `call-bound` directly [`5e124f9`](https://github.com/inspect-js/data-view-buffer/commit/5e124f92ef654f32c67d9eb12d19ecf1b42d8e6f)
- [Deps] update `call-bind`, `is-data-view` [`73fb1a4`](https://github.com/inspect-js/data-view-buffer/commit/73fb1a47dba2f1be13101770fc5dcae8e79a67fa)
- [Tests] replace `aud` with `npm audit` [`64563a3`](https://github.com/inspect-js/data-view-buffer/commit/64563a3cb83ee6ddc969de8fadf9379300933187)
- [Dev Deps] update `@ljharb/tsconfig` [`b842ce8`](https://github.com/inspect-js/data-view-buffer/commit/b842ce8adf17cf59158a3906a092d1dc5e6c10f9)
- [Deps] update `call-bind` [`bc89873`](https://github.com/inspect-js/data-view-buffer/commit/bc89873b8b4d657284a731f53cec9e69fe35057d)
- [Dev Deps] update `tape` [`b6ed8cb`](https://github.com/inspect-js/data-view-buffer/commit/b6ed8cb5804237d6f4e6bb2e3c1418c7ac3acc9b)
- [Dev Deps] add missing peer dep [`ba96a94`](https://github.com/inspect-js/data-view-buffer/commit/ba96a94c8d1afda58f302a4fc294a2633462b18a)

## [v1.0.1](https://github.com/inspect-js/data-view-buffer/compare/v1.0.0...v1.0.1) - 2024-02-06

### Commits

- [Refactor] use `es-errors`, so things that only need those do not need `get-intrinsic` [`675f588`](https://github.com/inspect-js/data-view-buffer/commit/675f588236e489268de9f7a0adf33ef8295a3c71)
- [Deps] update `call-bind`, `get-intrinsic` [`e6eb209`](https://github.com/inspect-js/data-view-buffer/commit/e6eb209578556ade150f0c3945cdeec9ffc582e2)

## v1.0.0 - 2024-02-02

### Commits

- Initial implementation, tests, readme, types [`2e1382b`](https://github.com/inspect-js/data-view-buffer/commit/2e1382b26a98acc6f2ade4a061e1cd829e3043b8)
- Initial commit [`1eb7dc4`](https://github.com/inspect-js/data-view-buffer/commit/1eb7dc4a0369d8320406bd7f9366c7887888790e)
- npm init [`d9e3d47`](https://github.com/inspect-js/data-view-buffer/commit/d9e3d4707b54bf77de30565ea5c6c0fcdf4a0ecc)
- Only apps should have lockfiles [`116b60b`](https://github.com/inspect-js/data-view-buffer/commit/116b60b9054de3331cd3c1599280466405c0d128)
