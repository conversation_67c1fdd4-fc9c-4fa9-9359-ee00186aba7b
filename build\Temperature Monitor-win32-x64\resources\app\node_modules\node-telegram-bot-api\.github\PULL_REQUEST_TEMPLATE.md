<!--
Mark whichever option below applies to this PR.
For example, if your PR passes all tests, you would mark the option as so:
- [x] All tests pass
Note the 'x' in between the square brackets '[]'
-->
- [ ] All tests pass
- [ ] I have run `npm run doc`

### Description

<!-- Explain what you are trying to achieve with this PR -->

### References

<!--
Add references to other documents/pages that are relevant to this
PR, such as related issues, documentation, etc.

For example,
* Issue #1: https://github.com/yagop/node-telegram-bot-api/issues/1
* Telegram Bot API - Getting updates: https://core.telegram.org/bots/api#getting-updates
-->
