<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Temperature Monitor</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
            height: 100vh;
            overflow: hidden;
        }

        .container {
            max-width: 650px;
            margin: 20px auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            padding: 30px;
            max-height: calc(100vh - 40px);
            display: flex;
            flex-direction: column;
            overflow-y: auto;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
        }

        .header h1 {
            color: #4a5568;
            font-size: 28px;
            font-weight: 600;
        }

        .temp-display {
            background: #f7fafc;
            border-radius: 12px;
            padding: 25px;
            margin-bottom: 25px;
            border: 2px solid #e2e8f0;
        }

        .temp-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            padding: 10px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        }

        .temp-item:last-child {
            margin-bottom: 0;
        }

        .temp-label {
            font-weight: 600;
            color: #4a5568;
        }

        .temp-value {
            font-size: 20px;
            font-weight: bold;
            padding: 5px 15px;
            border-radius: 20px;
            background: #e2e8f0;
            color: #4a5568;
        }

        .temp-normal { background: #c6f6d5; color: #22543d; }
        .temp-warning { background: #fed7d7; color: #c53030; }
        .temp-na { background: #e2e8f0; color: #718096; }

        .controls {
            display: flex;
            gap: 15px;
            margin-bottom: 25px;
            justify-content: center;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            min-width: 120px;
        }

        .btn-primary {
            background: #4299e1;
            color: white;
        }

        .btn-primary:hover {
            background: #3182ce;
            transform: translateY(-2px);
        }

        .btn-secondary {
            background: #718096;
            color: white;
        }

        .btn-secondary:hover {
            background: #4a5568;
            transform: translateY(-2px);
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .log-container {
            flex: 1;
            background: #f7fafc;
            border-radius: 12px;
            padding: 20px;
            border: 2px solid #e2e8f0;
            display: flex;
            flex-direction: column;
            min-height: 200px;
        }

        .log-header {
            font-weight: 600;
            color: #4a5568;
            margin-bottom: 15px;
            font-size: 16px;
        }

        .log-content {
            flex: 1;
            background: white;
            border-radius: 8px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            overflow-y: auto;
            border: 1px solid #e2e8f0;
            max-height: 200px;
        }

        .log-entry {
            margin-bottom: 5px;
            padding: 2px 0;
        }

        .log-timestamp {
            color: #718096;
        }

        .log-alert {
            color: #c53030;
            font-weight: bold;
        }

        .log-info {
            color: #4a5568;
        }

        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }

        .status-normal { background: #48bb78; }
        .status-alert { background: #f56565; animation: pulse 1s infinite; }
        .status-offline { background: #a0aec0; }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 1000;
        }

        .modal-content {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: white;
            border-radius: 12px;
            padding: 30px;
            width: 90%;
            max-width: 500px;
            max-height: 80vh;
            overflow-y: auto;
        }

        .modal-header {
            font-size: 20px;
            font-weight: 600;
            margin-bottom: 20px;
            color: #4a5568;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-label {
            display: block;
            margin-bottom: 5px;
            font-weight: 600;
            color: #4a5568;
        }

        .form-input {
            width: 100%;
            padding: 10px;
            border: 2px solid #e2e8f0;
            border-radius: 6px;
            font-size: 14px;
        }

        .form-input:focus {
            outline: none;
            border-color: #4299e1;
        }

        .form-checkbox {
            margin-right: 8px;
        }

        .modal-buttons {
            display: flex;
            gap: 10px;
            justify-content: flex-end;
            margin-top: 25px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🌡️ Temperature Monitor</h1>
        </div>

        <div class="temp-display">
            <div class="temp-item">
                <span class="temp-label">
                    <span class="status-indicator status-offline" id="cpu-status"></span>
                    CPU Temperature:
                </span>
                <span class="temp-value temp-na" id="cpu-temp">-- °C</span>
            </div>
            <div class="temp-item">
                <span class="temp-label">
                    <span class="status-indicator status-offline" id="hdd-status"></span>
                    HDD Temperature:
                </span>
                <span class="temp-value temp-na" id="hdd-temp">-- °C</span>
            </div>
            <div class="temp-item">
                <span class="temp-label">
                    <span class="status-indicator status-offline" id="cpu-usage-status"></span>
                    CPU Usage:
                </span>
                <span class="temp-value temp-na" id="cpu-usage">-- %</span>
            </div>
            <div class="temp-item">
                <span class="temp-label">
                    <span class="status-indicator status-offline" id="ram-usage-status"></span>
                    RAM Usage:
                </span>
                <span class="temp-value temp-na" id="ram-usage">-- %</span>
            </div>
            <div class="temp-item">
                <span class="temp-label">
                    <span class="status-indicator status-offline" id="hdd-usage-status"></span>
                    HDD Usage:
                </span>
                <span class="temp-value temp-na" id="hdd-usage">-- %</span>
            </div>
            <div class="temp-item">
                <span class="temp-label">
                    <span class="status-indicator status-offline" id="system-status"></span>
                    System Status:
                </span>
                <span class="temp-value temp-na" id="system-status-text">Offline</span>
            </div>
        </div>

        <div class="controls">
            <button class="btn btn-primary" id="start-btn" onclick="toggleMonitoring()">Start Monitoring</button>
            <button class="btn btn-secondary" onclick="openSettings()">Settings</button>
        </div>

        <div class="log-container">
            <div class="log-header">Activity Log</div>
            <div class="log-content" id="log-content"></div>
        </div>
    </div>

    <!-- Settings Modal -->
    <div class="modal" id="settings-modal">
        <div class="modal-content">
            <div class="modal-header">Settings</div>
            
            <div class="form-group">
                <label class="form-label">Telegram Bot Token:</label>
                <input type="password" class="form-input" id="telegram-token" placeholder="Enter bot token">
            </div>
            
            <div class="form-group">
                <label class="form-label">Telegram Chat ID:</label>
                <input type="text" class="form-input" id="telegram-chat-id" placeholder="Enter chat ID">
            </div>
            
            <div class="form-group">
                <label class="form-label">Refresh Interval (seconds):</label>
                <input type="number" class="form-input" id="refresh-interval" min="1" max="300" value="5">
            </div>
            
            <div class="form-group">
                <label class="form-label">CPU Threshold (°C):</label>
                <input type="number" class="form-input" id="cpu-threshold" min="30" max="100" value="80">
            </div>
            
            <div class="form-group">
                <label class="form-label">HDD Threshold (°C):</label>
                <input type="number" class="form-input" id="hdd-threshold" min="20" max="80" value="50">
            </div>
            
            <div class="form-group">
                <label class="form-label">
                    <input type="checkbox" class="form-checkbox" id="alert-enabled" checked>
                    Enable Telegram Alerts
                </label>
            </div>
            
            <div class="modal-buttons">
                <button class="btn btn-secondary" onclick="testTelegram()">Test Telegram</button>
                <button class="btn btn-secondary" onclick="closeSettings()">Cancel</button>
                <button class="btn btn-primary" onclick="saveSettings()">Save</button>
            </div>
        </div>
    </div>

    <script src="renderer.js"></script>
</body>
</html>
