{"name": "ecc-jsbn", "version": "0.1.2", "description": "ECC JS code based on JSBN", "main": "index.js", "repository": {"type": "git", "url": "https://github.com/quartzjer/ecc-jsbn.git"}, "keywords": ["jsbn", "ecc", "browserify"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://jeremie.com/"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://jeremie.com/"}, {"name": "<PERSON>", "url": "https://github.com/rynomad"}], "dependencies": {"jsbn": "~0.1.0", "safer-buffer": "^2.1.0"}, "license": "MIT", "bugs": {"url": "https://github.com/quartzjer/ecc-jsbn/issues"}, "homepage": "https://github.com/quartzjer/ecc-jsbn"}