{"name": "arraybuffer.prototype.slice", "version": "1.0.4", "description": "ES spec-compliant shim for ArrayBuffer.prototype.slice", "author": "<PERSON> <<EMAIL>>", "funding": {"url": "https://github.com/sponsors/ljharb"}, "license": "MIT", "main": "index.js", "exports": {".": "./index.js", "./auto": "./auto.js", "./polyfill": "./polyfill.js", "./implementation": "./implementation.js", "./shim": "./shim.js", "./package.json": "./package.json"}, "scripts": {"prepack": "npmignore --auto --commentLines=autogenerated", "prepublish": "not-in-publish || npm run prepublishOnly", "prepublishOnly": "safe-publish-latest", "pretest": "npm run lint", "test": "npm run tests-only", "posttest": "npx npm@'>=10.2' audit --production", "tests-only": "nyc tape 'test/**/*.js'", "prelint": "eclint check $(git ls-files | xargs find 2> /dev/null | grep -vE 'node_modules|\\.git')", "lint": "eslint --ext=js,mjs .", "postlint": "evalmd README.md && es-shim-api --bound", "version": "auto-changelog && git add CHANGELOG.md", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\""}, "repository": {"type": "git", "url": "git+https://github.com/es-shims/ArrayBuffer.prototype.slice.git"}, "homepage": "https://github.com/es-shims/ArrayBuffer.prototype.slice#readme", "bugs": {"url": "https://github.com/es-shims/ArrayBuffer.prototype.slice/issues"}, "keywords": ["javascript", "ecmascript", "ArrayBuffer.prototype.slice", "polyfill", "shim", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "array", "buffer", "ArrayBuffer#slice", "slice", "typed array", "es-shim API"], "dependencies": {"array-buffer-byte-length": "^1.0.1", "call-bind": "^1.0.8", "define-properties": "^1.2.1", "es-abstract": "^1.23.5", "es-errors": "^1.3.0", "get-intrinsic": "^1.2.6", "is-array-buffer": "^3.0.4"}, "devDependencies": {"@es-shims/api": "^2.5.1", "@ljharb/eslint-config": "^21.1.1", "auto-changelog": "^2.5.0", "eclint": "^2.8.1", "encoding": "^0.1.13", "es-value-fixtures": "^1.5.0", "eslint": "=8.8.0", "evalmd": "^0.0.19", "for-each": "^0.3.3", "functions-have-names": "^1.2.3", "has-strict-mode": "^1.0.1", "in-publish": "^2.0.1", "npmignore": "^0.3.1", "nyc": "^10.3.2", "object-inspect": "^1.13.3", "safe-publish-latest": "^2.0.0", "tape": "^5.9.0"}, "testling": {"files": "test/index.js"}, "engines": {"node": ">= 0.4"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false, "hideCredit": true}, "publishConfig": {"ignore": [".github/workflows"]}}