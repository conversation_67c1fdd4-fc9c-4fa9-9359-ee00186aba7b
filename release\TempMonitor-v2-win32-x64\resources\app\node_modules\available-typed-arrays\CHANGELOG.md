# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/)
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [v1.0.7](https://github.com/inspect-js/available-typed-arrays/compare/v1.0.6...v1.0.7) - 2024-02-19

### Commits

- [Refactor] use `possible-typed-array-names` [`ac86abf`](https://github.com/inspect-js/available-typed-arrays/commit/ac86abfd64c4b633fd6523cc4193f1913fd22666)

## [v1.0.6](https://github.com/inspect-js/available-typed-arrays/compare/v1.0.5...v1.0.6) - 2024-01-31

### Commits

- [actions] reuse common workflows [`1850353`](https://github.com/inspect-js/available-typed-arrays/commit/1850353ded0ceb4d02d9d05649da5b7f3a28c89f)
- [meta] use `npmignore` to autogenerate an npmignore file [`5c7de12`](https://github.com/inspect-js/available-typed-arrays/commit/5c7de120d22a5c35f703ba3f0b5287e5c5f38af6)
- [patch] add types [`fcfb0ea`](https://github.com/inspect-js/available-typed-arrays/commit/fcfb0ea21c9dc8459d68f8bb26679abb0bec71ca)
- [actions] update codecov uploader [`d844945`](https://github.com/inspect-js/available-typed-arrays/commit/d84494596881a298aabde9bd87e538ce10c6cd01)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `array.prototype.every`, `safe-publish-latest`, `tape` [`a2be6f4`](https://github.com/inspect-js/available-typed-arrays/commit/a2be6f482010e920692d8f65fe1f193dbb73004d)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `aud`, `auto-changelog`, `tape` [`b283a3e`](https://github.com/inspect-js/available-typed-arrays/commit/b283a3e2176fbe8e431a27e20df21c831f216d5a)
- [actions] update rebase action to use reusable workflow [`0ad1f2d`](https://github.com/inspect-js/available-typed-arrays/commit/0ad1f2d82b11713ee48d9b37cb73fcc891bd9f4a)
- [Dev Deps] update `@ljharb/eslint-config`, `array.prototype.every`, `aud`, `tape` [`cd36e81`](https://github.com/inspect-js/available-typed-arrays/commit/cd36e8131076dd4e67a88b259f829067fa56c139)
- [meta] simplify "exports" [`f696e5f`](https://github.com/inspect-js/available-typed-arrays/commit/f696e5ff9ded838e192ade4e8550a890c4f35eb0)
- [Dev Deps] update `aud`, `npmignore`, `tape` [`bf20080`](https://github.com/inspect-js/available-typed-arrays/commit/bf200809aea3107b31fc8817122c693e099be30e)

## [v1.0.5](https://github.com/inspect-js/available-typed-arrays/compare/v1.0.4...v1.0.5) - 2021-08-30

### Fixed

- [Refactor] use `globalThis` if available [`#12`](https://github.com/inspect-js/available-typed-arrays/issues/12)

### Commits

- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `tape` [`1199790`](https://github.com/inspect-js/available-typed-arrays/commit/1199790ab5841517ad04827fab3f135d2dc5cfb7)

## [v1.0.4](https://github.com/inspect-js/available-typed-arrays/compare/v1.0.3...v1.0.4) - 2021-05-25

### Commits

- [Refactor] Remove `array.prototype.filter` dependency [`f39c90e`](https://github.com/inspect-js/available-typed-arrays/commit/f39c90ecb1907de28ee2d3577b7da37ae12aac56)
- [Dev Deps] update `eslint`, `auto-changelog` [`b2e3a03`](https://github.com/inspect-js/available-typed-arrays/commit/b2e3a035e8cd3ddfd7b565249e1651c6419a34d0)
- [meta] create `FUNDING.yml` [`8c0e758`](https://github.com/inspect-js/available-typed-arrays/commit/8c0e758c6ec80adbb3770554653cdc3aa16beb55)
- [Tests] fix harmony test matrix [`ef96549`](https://github.com/inspect-js/available-typed-arrays/commit/ef96549df171776267529413240a2219cb59d5ce)
- [meta] add `sideEffects` flag [`288cca0`](https://github.com/inspect-js/available-typed-arrays/commit/288cca0fbd214bec706447851bb8bccc4b899a48)

## [v1.0.3](https://github.com/inspect-js/available-typed-arrays/compare/v1.0.2...v1.0.3) - 2021-05-19

### Commits

- [Tests] migrate tests to Github Actions [`3ef082c`](https://github.com/inspect-js/available-typed-arrays/commit/3ef082caaa153b49f4c37c85bbd5c4b13fe4f638)
- [meta] do not publish github action workflow files [`fd95ffd`](https://github.com/inspect-js/available-typed-arrays/commit/fd95ffdaca759eca81cb4c5d5772ee863dfea501)
- [actions] use `node/install` instead of `node/run`; use `codecov` action [`eb6bd65`](https://github.com/inspect-js/available-typed-arrays/commit/eb6bd659a31c92a6a178c71a89fe0d5261413e6c)
- [Tests] run `nyc` on all tests [`636c946`](https://github.com/inspect-js/available-typed-arrays/commit/636c94657b532599ef90a214aaa12639d11b0161)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `aud`, `auto-changelog`, `tape` [`70a3b61`](https://github.com/inspect-js/available-typed-arrays/commit/70a3b61367b318fb883c2f35b8f2d539849a23b6)
- [actions] add "Allow Edits" workflow [`bd09c45`](https://github.com/inspect-js/available-typed-arrays/commit/bd09c45299e396fa5bbd5be4c58b1aedcb372a82)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `array.prototype.every`, `aud`, `tape` [`8f97523`](https://github.com/inspect-js/available-typed-arrays/commit/8f9752308390a79068cd431436bbfd77bca15647)
- [readme] fix URLs [`75418e2`](https://github.com/inspect-js/available-typed-arrays/commit/75418e20b57f4ad5e65d8c2e1864efd14eaa2e65)
- [readme] add actions and codecov badges [`4a8bc30`](https://github.com/inspect-js/available-typed-arrays/commit/4a8bc30af2ce1f48e2b28ab3db5be9589bd6f2d0)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `aud` [`65198ac`](https://github.com/inspect-js/available-typed-arrays/commit/65198ace335a013ef49b6bd722bc80bbbc6be784)
- [actions] update workflows [`7f816eb`](https://github.com/inspect-js/available-typed-arrays/commit/7f816eb231131e53ced2572ba6c6c6a00f975789)
- [Refactor] use `array.prototype.filter` instead of `array-filter` [`2dd1038`](https://github.com/inspect-js/available-typed-arrays/commit/2dd1038d71ce48b5650687691cf8fe09795a6d30)
- [actions] switch Automatic Rease workflow to `pull_request_target` event [`9b45e91`](https://github.com/inspect-js/available-typed-arrays/commit/9b45e914fcb08bdaaaa0166b41716e51f400d1c6)
- [Dev Deps] update `auto-changelog`, `tape` [`0003a5b`](https://github.com/inspect-js/available-typed-arrays/commit/0003a5b122a0724db5499c114104eeeb396b2f67)
- [meta] use `prepublishOnly` script for npm 7+ [`d884dd1`](https://github.com/inspect-js/available-typed-arrays/commit/d884dd1c1117411f35d9fbc07f513a1a85ccdead)
- [readme] remove travis badge [`9da2b3c`](https://github.com/inspect-js/available-typed-arrays/commit/9da2b3c29706340fada995137aba12cfae4d6f37)
- [Dev Deps] update `auto-changelog`; add `aud` [`41b1336`](https://github.com/inspect-js/available-typed-arrays/commit/41b13369c71b0e3e57b9de0f4fb1e4d67950d74a)
- [Tests] only audit prod deps [`2571826`](https://github.com/inspect-js/available-typed-arrays/commit/2571826a5d121eeeeccf4c711e3f9e4616685d50)

## [v1.0.2](https://github.com/inspect-js/available-typed-arrays/compare/v1.0.1...v1.0.2) - 2020-01-26

### Commits

- [actions] add automatic rebasing / merge commit blocking [`3229a74`](https://github.com/inspect-js/available-typed-arrays/commit/3229a74bda60f24e2257efc40ddff9a3ce98de76)
- [Dev Deps] update `@ljharb/eslint-config` [`9579abe`](https://github.com/inspect-js/available-typed-arrays/commit/9579abecc196088561d3aedf27cad45b56f8e18b)
- [Fix] remove `require` condition to avoid experimental warning [`2cade6b`](https://github.com/inspect-js/available-typed-arrays/commit/2cade6b56d6a508a950c7da27d038bee496e716b)

## [v1.0.1](https://github.com/inspect-js/available-typed-arrays/compare/v1.0.0...v1.0.1) - 2020-01-24

### Commits

- [meta] add "exports" [`5942917`](https://github.com/inspect-js/available-typed-arrays/commit/5942917aafb56c6bce80f01b7ae6a9b46bc72c69)

## v1.0.0 - 2020-01-24

### Commits

- Initial commit [`2bc5144`](https://github.com/inspect-js/available-typed-arrays/commit/2bc514459c9f65756adfbd9964abf433183d78f6)
- readme [`31e4796`](https://github.com/inspect-js/available-typed-arrays/commit/31e4796379eba4a16d3c6a8e9baf6eb3f39e33d1)
- npm init [`9194266`](https://github.com/inspect-js/available-typed-arrays/commit/9194266b471a2a2dd5e6969bc40358ceb346e21e)
- Tests [`b539830`](https://github.com/inspect-js/available-typed-arrays/commit/b539830c3213f90de42b4d6e62803f52daf61a6d)
- Implementation [`6577df2`](https://github.com/inspect-js/available-typed-arrays/commit/6577df244ea146ef5ec16858044c8955e0fc445c)
- [meta] add `auto-changelog` [`7b43310`](https://github.com/inspect-js/available-typed-arrays/commit/7b43310be76f00fe60b74a2fd6d0e46ac1d01f3e)
- [Tests] add `npm run lint` [`dedfbc1`](https://github.com/inspect-js/available-typed-arrays/commit/dedfbc1592f86ac1636267d3965f2345df43815b)
- [Tests] use shared travis-ci configs [`c459d78`](https://github.com/inspect-js/available-typed-arrays/commit/c459d78bf2efa9d777f88599ae71a796dbfcb70f)
- Only apps should have lockfiles [`d294668`](https://github.com/inspect-js/available-typed-arrays/commit/d294668422cf35f5e7716a85bfd204e62b01c056)
- [meta] add `funding` field [`6e70bc1`](https://github.com/inspect-js/available-typed-arrays/commit/6e70bc1fb199c7898165aaf05c25bb49f4062e53)
- [meta] add `safe-publish-latest` [`dd89ca2`](https://github.com/inspect-js/available-typed-arrays/commit/dd89ca2c6842f0f3e82958df2b2bd0fc0c929c51)
