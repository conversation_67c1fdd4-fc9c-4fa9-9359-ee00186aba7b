{"name": "@cypress/request-promise", "version": "5.0.0", "description": "Cypress's fork of the simplified HTTP request client 'request' with Promise support. Powered by Bluebird.", "keywords": ["xhr", "http", "https", "promise", "request", "then", "thenable", "bluebird"], "main": "./lib/rp.js", "scripts": {"test": "./node_modules/.bin/gulp ci", "test-publish": "./node_modules/.bin/gulp ci-no-cov", "publish-please": "publish-please"}, "repository": {"type": "git", "url": "git+https://github.com/cypress-io/request-promise.git"}, "author": "<PERSON><PERSON> (https://github.com/analog-nico)", "license": "ISC", "bugs": {"url": "https://github.com/cypress-io/request-promise/issues"}, "homepage": "https://github.com/cypress-io/request-promise#readme", "engines": {"node": ">=0.10.0"}, "dependencies": {"request-promise-core": "1.1.3", "bluebird": "^3.5.0", "stealthy-require": "^1.1.1", "tough-cookie": "^4.1.3"}, "peerDependencies": {"@cypress/request": "^3.0.0"}, "devDependencies": {"body-parser": "~1.15.2", "chai": "~3.5.0", "chalk": "~1.1.3", "gulp": "~3.9.1", "gulp-coveralls": "~0.1.4", "gulp-eslint": "~2.1.0", "gulp-istanbul": "~1.0.0", "gulp-mocha": "~2.2.0", "lodash": "~4.13.1", "publish-please": "~2.1.4", "@cypress/request": "^3.0.0", "rimraf": "~2.5.3", "run-sequence": "~1.2.2"}, "resolutions": {"chalk": "~1.1.3"}}