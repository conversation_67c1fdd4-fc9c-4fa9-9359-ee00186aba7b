{"name": "<PERSON><PERSON><PERSON>", "description": "A light, featureful and explicit option parsing library.", "version": "1.14.1", "author": "<PERSON> <<EMAIL>> (http://trentm.com)", "keywords": ["option", "parser", "parsing", "cli", "command", "args", "bash", "completion"], "repository": {"type": "git", "url": "git://github.com/trentm/node-dashdash.git"}, "main": "./lib/dashdash.js", "dependencies": {"assert-plus": "^1.0.0"}, "devDependencies": {"nodeunit": "0.9.x"}, "engines": {"node": ">=0.10"}, "scripts": {"test": "nodeunit test/*.test.js"}, "license": "MIT"}