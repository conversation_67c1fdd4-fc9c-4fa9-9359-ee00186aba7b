{"name": "caseless", "version": "0.12.0", "description": "Caseless object set/get/has, very useful when working with HTTP headers.", "main": "index.js", "scripts": {"test": "node test.js"}, "repository": {"type": "git", "url": "https://github.com/mikeal/caseless"}, "keywords": ["headers", "http", "caseless"], "test": "node test.js", "author": "<PERSON><PERSON> <<EMAIL>>", "license": "Apache-2.0", "bugs": {"url": "https://github.com/mikeal/caseless/issues"}, "devDependencies": {"tape": "^2.10.2"}}