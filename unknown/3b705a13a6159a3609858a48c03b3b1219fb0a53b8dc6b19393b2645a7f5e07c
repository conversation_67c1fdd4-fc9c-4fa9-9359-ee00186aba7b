# 🌡️ Temperature Monitor - Cách sửa lỗi không đọc được nhiệt độ

## ❌ Vấn đề: App không hiển thị nhiệt độ CPU/HDD

### 🔧 Nguyên nhân:
App cần **OpenHardwareMonitor** hoặc **LibreHardwareMonitor** chạy trong background để đọc nhiệt độ qua WMI.

### ✅ Giải pháp:

#### Cách 1: OpenHardwareMonitor (Khuyến nghị)
1. **Download**: https://openhardwaremonitor.org/downloads/
2. **Tải file**: `openhardwaremonitor-v0.9.6.zip`
3. **Giải nén** và chạy `OpenHardwareMonitor.exe`
4. **Chạy as Administrator** (quan trọng!)
5. **Minimize** xuống system tray
6. **Restart** Temperature Monitor app

#### Cách 2: LibreHardwareMonitor (Alternative)
1. **Download**: https://github.com/LibreHardwareMonitor/LibreHardwareMonitor/releases
2. **Tải file**: `LibreHardwareMonitor-net472.zip`
3. **Giải nén** và chạy `LibreHardwareMonitor.exe`
4. **Chạy as Administrator**
5. **Minimize** xuống system tray
6. **Restart** Temperature Monitor app

### 🔍 Kiểm tra:
App sẽ tự động detect và hiển thị message trong console:
- ✅ "OpenHardwareMonitor is running" = OK
- ⚠️  "OpenHardwareMonitor not detected" = Cần cài

### 📋 Lưu ý:
- **Phải chạy as Administrator** để access hardware sensors
- **Để chạy background** - không tắt OpenHardwareMonitor
- **Restart app** sau khi cài OpenHardwareMonitor
- **Windows 10/11** có thể block, cho phép trong Windows Defender

### 🎯 Sau khi cài:
App sẽ hiển thị:
- 🌡️ CPU Temperature (°C)
- 💾 HDD Temperature (°C)
- ⚡ CPU Usage (%)
- 🧠 RAM Usage (%)
- 💿 Disk Usage (%)

### 🚨 Nếu vẫn không work:
1. Restart máy
2. Chạy OpenHardwareMonitor trước
3. Chạy Temperature Monitor sau
4. Check Windows Event Viewer for errors
