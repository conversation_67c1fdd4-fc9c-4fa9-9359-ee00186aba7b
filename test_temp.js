// Test temperature detection methods
const { exec } = require('child_process');

async function testTemperatureMethods() {
    console.log('Testing temperature detection methods...\n');
    
    // Test LibreHardwareMonitor
    console.log('1. Testing LibreHardwareMonitor WMI...');
    await new Promise((resolve) => {
        exec('powershell "Get-WmiObject -Namespace root/LibreHardwareMonitor -Class Sensor | Where-Object {$_.SensorType -eq \'Temperature\'} | Select-Object Name, Value"', 
            (error, stdout) => {
                if (error) {
                    console.log('   ❌ LibreHardwareMonitor not available');
                } else {
                    console.log('   ✅ LibreHardwareMonitor found:');
                    console.log(stdout);
                }
                resolve();
            });
    });
    
    // Test OpenHardwareMonitor
    console.log('\n2. Testing OpenHardwareMonitor WMI...');
    await new Promise((resolve) => {
        exec('powershell "Get-WmiObject -Namespace root/OpenHardwareMonitor -Class Sensor | Where-Object {$_.SensorType -eq \'Temperature\'} | Select-Object Name, Value"', 
            (error, stdout) => {
                if (error) {
                    console.log('   ❌ OpenHardwareMonitor not available');
                } else {
                    console.log('   ✅ OpenHardwareMonitor found:');
                    console.log(stdout);
                }
                resolve();
            });
    });
    
    // Test WMIC thermal zone
    console.log('\n3. Testing WMIC thermal zone...');
    await new Promise((resolve) => {
        exec('wmic /namespace:\\\\root\\wmi PATH MSAcpi_ThermalZoneTemperature get CurrentTemperature /value', 
            (error, stdout) => {
                if (error) {
                    console.log('   ❌ WMIC thermal zone not available');
                } else {
                    console.log('   ✅ WMIC thermal zone found:');
                    console.log(stdout);
                }
                resolve();
            });
    });
    
    // Test SMART data
    console.log('\n4. Testing SMART temperature...');
    await new Promise((resolve) => {
        exec('powershell "Get-WmiObject -Namespace root/wmi -Class MSStorageDriver_FailurePredictTemperature | Select-Object CurrentTemperature"', 
            (error, stdout) => {
                if (error) {
                    console.log('   ❌ SMART temperature not available');
                } else {
                    console.log('   ✅ SMART temperature found:');
                    console.log(stdout);
                }
                resolve();
            });
    });
    
    console.log('\nTest completed!');
}

testTemperatureMethods();
