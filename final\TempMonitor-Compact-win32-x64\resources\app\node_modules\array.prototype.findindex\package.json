{"name": "array.prototype.findindex", "version": "2.2.4", "description": "Array.prototype.findIndex ES2015 polyfill.", "keywords": ["Array.prototype.findIndex", "findIndex", "es6", "es2015"], "main": "index.js", "scripts": {"prepack": "npmignore --auto --commentLines=autogenerated", "prepublishOnly": "safe-publish-latest", "prepublish": "not-in-publish || npm run prepublishOnly", "prelint": "es-shim-api --bound", "lint": "eslint --ext=js,mjs .", "pretest": "npm run lint", "tests-only": "nyc tape 'test/**/*.js'", "test": "npm run tests-only", "posttest": "npx npm@'>= 10.2' audit --production", "version": "auto-changelog && git add CHANGELOG.md", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\""}, "repository": {"type": "git", "url": "git://github.com/paulmillr/Array.prototype.findIndex.git"}, "author": "<PERSON> <http://paulmillr.com>", "license": "MIT", "bugs": {"url": "https://github.com/paulmillr/Array.prototype.findIndex/issues"}, "devDependencies": {"@es-shims/api": "^2.5.1", "@ljharb/eslint-config": "^21.1.1", "auto-changelog": "^2.5.0", "encoding": "^0.1.13", "eslint": "=8.8.0", "functions-have-names": "^1.2.3", "has-strict-mode": "^1.0.1", "in-publish": "^2.0.1", "npmignore": "^0.3.1", "nyc": "^10.3.2", "safe-publish-latest": "^2.0.0", "tape": "^5.9.0"}, "dependencies": {"call-bind": "^1.0.8", "call-bound": "^1.0.3", "define-properties": "^1.2.1", "es-abstract": "^1.23.6", "es-object-atoms": "^1.0.0", "es-shim-unscopables": "^1.0.2"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false, "hideCredit": true, "startingVersion": "2.2.2"}, "publishConfig": {"ignore": [".github/workflows"]}, "engines": {"node": ">= 0.4"}}